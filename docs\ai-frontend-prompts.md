# 结直肠癌筛查微观模拟模型 - AI前端开发提示集

## 🎯 项目概述提示（用于v0、Lovable等AI工具）

```
请为结直肠癌筛查微观模拟模型创建一个专业的科学研究界面。这是一个面向医疗政策制定者、流行病学专家和卫生经济学家的复杂数据分析平台。

核心特点：
- 科学研究级别的专业界面
- 复杂参数配置的渐进式披露
- 大规模数据可视化（支持100万个体）
- 机器学习校准过程监控
- 长期模拟执行监控（100年周期）
- 全面的卫生经济学分析

技术要求：
- 使用React + TypeScript
- 响应式设计（移动端到宽屏）
- WCAG 2.1 AA无障碍合规
- 高性能数据可视化
- 模块化组件架构

设计原则：
- 科学可信度优先 - 专业界面建立对复杂分析结果的信心
- 渐进式披露 - 复杂参数按逻辑工作流组织，避免认知过载
- 透明度至上 - 清晰展示模型假设、计算过程和数据来源
- 可重现性支持 - 便于分享、记录和重复模拟配置

色彩方案：
- 主色调：#1565C0（专业蓝）
- 成功色：#2E7D32（深绿）
- 警告色：#F57C00（橙色）
- 错误色：#C62828（深红）
- 中性色：#FAFAFA, #F5F5F5, #EEEEEE

字体：
- 主要字体：Inter（现代、清晰、数字友好）
- 等宽字体：Roboto Mono（代码、数据显示）
```

## 🏠 主仪表板开发提示

```
创建结直肠癌筛查模拟系统的主仪表板，包含以下元素：

布局结构：
- 顶部导航栏：仪表板、模拟配置、结果分析、项目管理、数据管理、系统设置
- 左侧快速操作面板（宽度300px）
- 中央内容区域：项目状态卡片网格（3-4列响应式）
- 右侧系统状态面板（宽度280px）

关键组件：

1. 项目状态卡片
   - 显示项目名称、状态（进行中/已完成/草稿）
   - 环形进度条显示完成百分比
   - 关键指标：模拟人数、运行时间、最后更新
   - 快速操作按钮：查看结果、继续配置、复制项目
   - 支持拖拽排序，悬停效果

2. 快速操作面板
   - 新建项目（大按钮，主要CTA，#1565C0背景）
   - 导入数据文件（支持拖拽上传）
   - 查看最新报告
   - 系统健康检查
   - 最近使用的模板列表

3. 系统状态面板
   - 当前运行的模拟数量（实时更新）
   - 系统资源使用率（CPU、内存进度条）
   - 最近活动时间线（最近10条操作）
   - 关键统计摘要（总项目数、成功率）

设计要求：
- 卡片式布局，8px圆角，subtle阴影
- 清晰的视觉层次，合理的间距
- 响应式网格系统（移动端单列，桌面端多列）
- 加载状态（骨架屏）和空状态处理
- 实时数据更新指示器（小圆点闪烁）

技术实现：
- 使用CSS Grid和Flexbox布局
- 实现虚拟滚动（大量项目时）
- WebSocket连接用于实时更新
- 本地存储用户偏好设置
- React Query用于数据缓存
```

## ⚙️ 模拟配置向导提示

```
创建一个6步模拟配置向导，用于设置复杂的结直肠癌筛查模拟参数：

步骤结构：
1. 人群设置（人口统计学、生命表导入）
2. 疾病建模（双通路设置、风险因素）
3. 筛查策略（工具配置、时间线设计）
4. 经济参数（成本设置、经济指标）
5. 校准设置（基准值、机器学习配置）
6. 模拟执行（参数确认、启动模拟）

界面布局：
- 顶部：步骤进度指示器（带完成状态，绿色勾选）
- 左侧：参数配置面板（60%宽度，可折叠分组）
- 右侧：预览面板（40%宽度，配置摘要）
- 底部：操作栏（上一步、下一步、保存草稿、退出）

关键功能：

1. 实时参数验证
   - 红色边框标示错误字段
   - 内联错误消息和修正建议
   - 参数冲突检测和警告（黄色背景）
   - 必填字段标记（红色星号）

2. 智能帮助系统
   - 问号图标触发上下文帮助
   - 医学文献参考链接
   - 默认值建议和合理范围提示
   - 参数影响说明

3. 数据导入集成
   - 拖拽上传区域（虚线边框，悬停高亮）
   - 支持CSV、Excel、JSON格式
   - 文件格式验证和错误提示
   - 数据预览表格（前10行）
   - 列映射工具（下拉选择）

4. 配置预览
   - 参数摘要卡片（分组显示）
   - 预估运行时间和资源需求
   - 配置完整性检查（进度条）
   - 关键假设和限制说明

特殊组件：

1. 风险因素权重滑块
   - 6个风险因素：家族史、IBD、肥胖、糖尿病、吸烟、久坐
   - 0-3倍权重范围，默认值基于文献
   - 实时影响预览（小图表）

2. 筛查策略时间线
   - 可拖拽的时间轴（50-80岁）
   - 筛查工具图标（FIT、结肠镜等）
   - 间隔设置（年份输入）
   - 重叠检测和冲突警告

技术实现：
- 使用状态机管理步骤流程（XState）
- 表单验证使用React Hook Form + Yup
- 文件上传使用react-dropzone
- 数据表格使用@tanstack/react-table
- 图表使用recharts
- 拖拽功能使用@dnd-kit
```

## 📊 数据可视化组件提示

```
创建专业的科学数据可视化组件库，支持大规模数据展示：

核心图表组件：

1. 科学线图组件 (ScientificLineChart)
   - 支持多条数据线（最多10条）
   - X轴：时间或年龄，Y轴：发病率/死亡率
   - 可缩放和平移（鼠标滚轮、拖拽）
   - 数据点悬停详情（工具提示）
   - 置信区间阴影（半透明填充）
   - 图例（可点击隐藏/显示线条）
   - 导出功能（PNG、SVG、PDF按钮）
   - 网格线和坐标轴标签

2. 交互式柱状图 (InteractiveBarChart)
   - 分组和堆叠模式切换
   - 动态排序（升序/降序）
   - 数据筛选（复选框）
   - 数据钻取功能（点击展开详情）
   - 比较模式（并排显示多个数据集）
   - 动画过渡效果

3. 参数敏感性热力图 (SensitivityHeatmap)
   - 参数名称作为行列标签
   - 颜色渐变映射敏感性强度
   - 工具提示显示具体数值
   - 行列排序和分组
   - 颜色图例和数值范围

4. 成本效益散点图 (CostEffectivenessPlot)
   - X轴：增量效果，Y轴：增量成本
   - 气泡大小映射人群规模
   - 象限分割线（成本效益阈值）
   - 数据点选择和高亮
   - 策略标签和图例

5. 模拟进度监控 (SimulationProgress)
   - 环形进度指示器（年份进度）
   - 实时更新动画（平滑过渡）
   - 多层进度显示（总体/当前阶段）
   - 预估完成时间
   - 暂停/恢复控制

性能要求：
- 支持10万+数据点渲染
- 使用Canvas或WebGL加速
- 虚拟化长列表
- 数据分页和懒加载
- 内存使用优化（及时清理）

交互特性：
- 图表联动筛选（选择一个图表影响其他）
- 缩放和平移（鼠标和触摸）
- 数据选择和高亮
- 实时数据更新（WebSocket）
- 响应式适配（移动端简化显示）

技术实现：
- 使用D3.js进行复杂可视化
- Canvas渲染大数据集
- React.memo优化重渲染
- Web Workers处理数据计算
- IndexedDB缓存大数据集
- 使用Intersection Observer优化性能
```

## 🤖 机器学习界面提示

```
创建机器学习校准监控界面，用于深度神经网络训练过程：

主要区域布局：

1. 训练配置面板（左上，30%宽度）
   - 网络架构选择（下拉菜单：MLP、CNN、RNN）
   - 超参数设置：
     * 学习率（0.001-0.1，对数滑块）
     * 批次大小（32、64、128、256选择）
     * 隐藏层数（1-5层滑块）
     * 神经元数量（每层，64-512）
   - 训练数据配置：
     * 拉丁超立方抽样参数
     * 训练/验证集比例（80/20默认）
   - 校准目标权重设置（多个滑块）

2. 实时监控面板（右上，70%宽度）
   - 损失函数曲线（双Y轴：训练损失vs验证损失）
   - 参数收敛状态图（散点图显示参数变化）
   - 训练速度指标（每秒样本数，实时更新）
   - 资源使用监控：
     * GPU利用率（进度条，绿色）
     * 内存使用（进度条，蓝色）
     * CPU利用率（进度条，橙色）

3. 结果分析面板（左下，50%宽度）
   - 校准精度指标（R²、RMSE、MAE）
   - 置信区间计算结果（95%CI）
   - 参数分布可视化（直方图）
   - 收敛诊断图表（梯度范数）

4. 控制操作面板（右下，50%宽度）
   - 训练控制按钮：
     * 开始训练（绿色，大按钮）
     * 暂停/恢复（黄色）
     * 停止训练（红色）
   - 模型管理：
     * 保存检查点（自动+手动）
     * 加载预训练模型
     * 导出训练模型
   - 超参数实时调整（训练中可调）

特殊功能：

1. 早停建议系统
   - 自动检测过拟合（验证损失上升）
   - 建议最佳停止点（高亮显示）
   - 模型性能预测（趋势线）

2. 超参数优化助手
   - 自动调参建议（基于当前性能）
   - 网格搜索可视化（热力图）
   - 贝叶斯优化集成

3. 训练日志系统
   - 详细的训练历史（表格形式）
   - 错误和警告记录（红色/黄色标记）
   - 性能瓶颈分析（图表）
   - 导出日志功能

视觉设计：
- 深色主题（适合长时间监控）
- 实时更新的图表（平滑动画）
- 状态指示器（绿色=正常，红色=错误）
- 进度条和百分比显示

技术实现：
- WebSocket实时数据流
- 高频率图表更新优化（requestAnimationFrame）
- 训练状态持久化（localStorage）
- 后台任务管理（Service Worker）
- 错误恢复机制（自动重连）
```

## 📱 响应式布局提示

```
实现全面的响应式设计，支持从手机到宽屏的所有设备：

断点策略：
- 移动端：320px-767px（单列布局）
- 平板端：768px-1023px（两列布局）
- 桌面端：1024px-1439px（多列布局）
- 宽屏：1440px+（扩展布局）

布局适配规则：

移动端优化（320px-767px）：
- 汉堡菜单导航（三条横线图标）
- 全屏模态对话框
- 触摸友好的按钮尺寸（最小44px×44px）
- 垂直堆叠的表单布局
- 简化的图表显示（隐藏次要信息）
- 底部固定的主要操作按钮
- 单列卡片布局

平板端适配（768px-1023px）：
- 折叠式侧边栏（可展开/收起）
- 适应性模态框（不全屏）
- 两列表单布局
- 中等复杂度的图表
- 标签式导航
- 两列卡片网格

桌面端优化（1024px-1439px）：
- 固定侧边导航（始终可见）
- 多列数据展示（3-4列）
- 完整功能的图表
- 键盘快捷键支持
- 悬停效果和工具提示
- 右键上下文菜单

宽屏增强（1440px+）：
- 并排比较视图
- 多面板同时显示
- 扩展的数据表格（更多列）
- 高密度信息展示
- 分屏功能
- 多窗口支持

CSS实现示例：
```css
/* 移动端优先 */
.container {
  padding: 16px;
  display: flex;
  flex-direction: column;
}

/* 平板端 */
@media (min-width: 768px) {
  .container {
    padding: 24px;
    flex-direction: row;
    gap: 24px;
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .container {
    padding: 32px;
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* 宽屏 */
@media (min-width: 1440px) {
  .container {
    max-width: 1400px;
    gap: 32px;
  }
}
```

技术实现：
- CSS Grid和Flexbox结合使用
- 容器查询（@container）用于组件级响应式
- 响应式图片和图标（srcset）
- 触摸手势支持（hammer.js）
- 键盘导航优化
```

## ♿ 无障碍访问提示

```
确保界面符合WCAG 2.1 AA标准，支持所有用户：

视觉无障碍要求：
- 色彩对比度至少4.5:1（正常文本）
- 大文本（18px+）对比度至少3:1
- 不依赖颜色传达信息（使用图标+文字）
- 支持200%文本缩放而不丢失功能
- 高对比度模式支持
- 焦点指示器清晰可见（2px蓝色边框）

键盘导航要求：
- 所有交互元素可通过Tab键访问
- 逻辑的Tab顺序（从左到右，从上到下）
- Shift+Tab反向导航
- Enter和Space键激活按钮
- 箭头键导航菜单和列表
- Escape键关闭模态框

快捷键支持：
- Alt+1: 跳转到主内容
- Alt+2: 跳转到导航
- Alt+3: 跳转到搜索
- Ctrl+S: 保存当前配置
- Ctrl+N: 新建项目

屏幕阅读器支持：
- 语义化HTML结构（header、nav、main、aside、footer）
- 完整的ARIA标签：
  * aria-label: 描述元素用途
  * aria-describedby: 关联帮助文本
  * aria-expanded: 折叠/展开状态
  * aria-live: 动态内容更新通知
- 图表的替代文本描述
- 表格的标题和摘要
- 表单标签正确关联

认知无障碍：
- 清晰的错误消息（具体说明问题和解决方法）
- 一致的交互模式（相同操作在不同页面表现一致）
- 操作确认和撤销机制
- 进度指示和反馈
- 简洁的语言和术语解释

HTML示例：
```html
<!-- 语义化结构 -->
<main role="main" aria-labelledby="page-title">
  <h1 id="page-title">模拟配置</h1>

  <!-- 表单标签 -->
  <label for="population-size">人群规模</label>
  <input
    id="population-size"
    type="number"
    aria-describedby="population-help"
    aria-required="true"
  />
  <div id="population-help">请输入10,000到1,000,000之间的数值</div>

  <!-- 动态内容 -->
  <div aria-live="polite" id="status-updates">
    <!-- 状态更新会在这里显示 -->
  </div>

  <!-- 图表替代文本 -->
  <div role="img" aria-labelledby="chart-title" aria-describedby="chart-desc">
    <h3 id="chart-title">年龄特异性发病率趋势</h3>
    <p id="chart-desc">
      图表显示50-80岁人群的结直肠癌发病率随年龄增长而上升，
      从50岁的0.1%增长到80岁的2.3%。
    </p>
    <!-- 图表内容 -->
  </div>
</main>
```

技术实现：
- 使用语义化HTML元素
- 实现ARIA live regions
- 提供skip links（跳转链接）
- 测试屏幕阅读器兼容性（NVDA、JAWS）
- 键盘陷阱管理（模态框内导航）
- 颜色对比度自动检测
```

## ⚡ 性能优化提示

```
实现高性能的大数据处理界面，支持100万个体数据：

数据处理优化：
- 虚拟滚动大列表（react-window或react-virtualized）
- 数据分页和懒加载（每页1000条记录）
- Web Workers处理计算密集任务
- IndexedDB缓存大数据集（离线支持）
- 数据压缩和增量更新（只传输变化部分）

渲染优化：
- React.memo防止不必要重渲染
- useMemo缓存计算结果
- useCallback稳定函数引用
- 代码分割和懒加载组件（React.lazy）
- 图片懒加载和WebP格式

内存管理：
- 及时清理事件监听器（useEffect cleanup）
- 组件卸载时清理定时器
- 大数据集的流式处理
- 内存泄漏监控和预警
- 垃圾回收优化

网络优化：
- API请求去重和缓存（React Query）
- 图片和资源CDN加速
- 关键资源预加载（<link rel="preload">）
- 离线缓存策略（Service Worker）
- 请求合并和批处理

大数据可视化优化：
- Canvas渲染替代SVG（大数据集）
- WebGL加速（three.js或deck.gl）
- 数据聚合和采样显示
- LOD（细节层次）技术
- 视口裁剪（只渲染可见部分）

代码示例：
```javascript
// 虚拟滚动实现
import { FixedSizeList as List } from 'react-window';

const VirtualizedTable = ({ data }) => {
  const Row = ({ index, style }) => (
    <div style={style}>
      {data[index].name} - {data[index].value}
    </div>
  );

  return (
    <List
      height={600}
      itemCount={data.length}
      itemSize={50}
      width="100%"
    >
      {Row}
    </List>
  );
};

// Web Worker数据处理
const processDataInWorker = (data) => {
  return new Promise((resolve) => {
    const worker = new Worker('/data-processor.js');
    worker.postMessage(data);
    worker.onmessage = (e) => {
      resolve(e.data);
      worker.terminate();
    };
  });
};

// 内存优化的图表组件
const OptimizedChart = React.memo(({ data, width, height }) => {
  const processedData = useMemo(() => {
    // 数据采样，最多显示1000个点
    if (data.length > 1000) {
      const step = Math.ceil(data.length / 1000);
      return data.filter((_, index) => index % step === 0);
    }
    return data;
  }, [data]);

  return <Chart data={processedData} width={width} height={height} />;
});
```

监控和调试：
- 性能指标收集（Core Web Vitals）
- 错误边界和日志（Sentry）
- 开发工具集成（React DevTools）
- 用户体验监控（Real User Monitoring）
- Bundle分析（webpack-bundle-analyzer）
```

## 🔧 开发工具配置提示

```
推荐的开发环境和工具配置：

项目初始化：
```bash
# 使用Vite创建React项目
npm create vite@latest colorectal-screening-ui -- --template react-ts

# 安装核心依赖
npm install @tanstack/react-query @tanstack/react-table
npm install recharts d3 @types/d3
npm install react-hook-form @hookform/resolvers yup
npm install @dnd-kit/core @dnd-kit/sortable
npm install react-window react-window-infinite-loader

# 安装开发依赖
npm install -D @types/react @types/react-dom
npm install -D eslint @typescript-eslint/eslint-plugin
npm install -D prettier eslint-config-prettier
npm install -D @testing-library/react @testing-library/jest-dom
npm install -D @storybook/react @storybook/addon-essentials
```

TypeScript配置（tsconfig.json）：
```json
{
  "compilerOptions": {
    "target": "ES2020",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/utils/*": ["src/utils/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

ESLint配置（.eslintrc.js）：
```javascript
module.exports = {
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:jsx-a11y/recommended',
    'prettier'
  ],
  rules: {
    'react/react-in-jsx-scope': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'jsx-a11y/no-autofocus': 'off',
    'react/prop-types': 'off'
  }
};
```

推荐的VS Code扩展：
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Auto Rename Tag
- Prettier - Code formatter
- ESLint
- GitLens
- Thunder Client（API测试）
```
```
